import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:rescue_app/core/app.dart';
import 'package:rescue_app/core/providers/app_providers.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化Firebase
  await Firebase.initializeApp();

  runApp(
    MultiProvider(
      providers: AppProviders.providers,
      child: const RescueApp(),
    ),
  );
}
